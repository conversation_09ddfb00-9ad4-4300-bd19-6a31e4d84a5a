{
    "request": {
        "method": "POST",
        "urlPath": "/auth/login",
        "headers": {
            "Content-Type": {
                "equalTo": "application/json"
            }
        },
        "bodyPatterns": [
            {
                "matchesJsonPath": "$.username"
            },
            {
                "matchesJsonPath": "$.password"
            }
        ]
    },
    "response": {
        "status": 200,
        "transformers": ["JwtResponseTransformer"],
        "headers": {
            "Content-Type": "application/json"
        },
        "body": "{
            \"message\": \"Login successful\",
            \"user\": \"{{request.body 'username'}}\",
            \"token\": \"will_be_replaced_by_transformer\"
        }"
    }
}