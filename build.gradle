plugins {
    id 'groovy'
    id 'java'
    id 'application'
}

repositories {
    mavenCentral()
}

dependencies {
    implementation 'org.apache.groovy:groovy-all:4.0.15'
    implementation 'com.github.tomakehurst:wiremock-jre8:3.0.1'
    
    // Add these for better compatibility
    implementation 'com.github.tomakehurst:wiremock:3.0.1'
    implementation 'org.apache.groovy:groovy-json:4.0.15'
    
    // Jackson dependencies for Java transformers
    implementation 'com.fasterxml.jackson.core:jackson-core:2.15.2'
    implementation 'com.fasterxml.jackson.core:jackson-databind:2.15.2'
    implementation 'com.fasterxml.jackson.core:jackson-annotations:2.15.2'
}

sourceSets {
    main {
        groovy {
            srcDirs = ['extensions']
        }
        java {
            srcDirs = ['src/main/java']
        }
    }
}

// Explicitly set Java compatibility
java {
    toolchain {
        languageVersion = JavaLanguageVersion.of(21)
    }
}

// Create a fat JAR with all dependencies
jar {
    archiveFileName = 'wiremock-extensions.jar'
    from {
        configurations.runtimeClasspath.collect { it.isDirectory() ? it : zipTree(it) }
    }
    duplicatesStrategy = DuplicatesStrategy.EXCLUDE
}
