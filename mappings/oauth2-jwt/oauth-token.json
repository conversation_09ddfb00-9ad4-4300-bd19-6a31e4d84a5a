{"name": "Auth → Login endpoint (JWT generator)", "request": {"method": "POST", "urlPath": "/oauth/token", "headers": {"Content-Type": {"equalTo": "application/json"}}}, "response": {"status": 200, "transformers": ["jwt-generator-enhanced"], "headers": {"Content-Type": "application/json"}, "body": "{\"message\": \"Token will be generated by transformer\"}"}, "metadata": {"description": "Simulates login and returns a JWT token with 1-hour expiry", "tags": ["OAuth2", "JWT"]}}