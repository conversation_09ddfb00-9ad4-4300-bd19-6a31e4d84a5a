{"mappings": [{"name": "Login endpoint - generates JWT", "priority": 1, "request": {"method": "POST", "url": "/auth/login", "headers": {"Content-Type": {"equalTo": "application/json"}}, "bodyPatterns": [{"matchesJsonPath": "$.username"}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "transformers": ["jwt-generator"]}}, {"name": "Token refresh endpoint", "request": {"method": "POST", "url": "/auth/refresh", "headers": {"Authorization": {"matches": "Bearer .*"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "transformers": ["jwt-refresh"]}}, {"name": "Protected API endpoint", "priority": 1, "request": {"method": "GET", "url": "/api/protected", "headers": {"Authorization": {"matches": "Bearer .*"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "transformers": ["jwt-validator"], "jsonBody": {"data": "This is protected content", "timestamp": "{{now}}", "user": "{{request.headers.Authorization}}"}}}, {"name": "Protected API endpoint - no auth", "priority": 2, "request": {"method": "GET", "url": "/api/protected"}, "response": {"status": 401, "headers": {"Content-Type": "application/json"}, "jsonBody": {"error": "Unauthorized", "message": "Missing or invalid authorization header"}}}, {"name": "User profile endpoint", "request": {"method": "GET", "url": "/api/user/profile", "headers": {"Authorization": {"matches": "Bearer .*"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "transformers": ["jwt-validator"], "jsonBody": {"user_id": "extracted_from_jwt", "name": "Test User", "email": "<EMAIL>", "roles": ["user"]}}}]}