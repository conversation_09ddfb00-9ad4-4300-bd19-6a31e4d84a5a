{"request": {"method": "POST", "urlPath": "/api/TDSMimickingAuth", "headers": {"Content-Type": {"equalTo": "application/json"}}}, "response": {"status": 200, "fixedDelayMilliseconds": 3000, "transformers": ["password-auth-transformer"], "headers": {"Content-Type": "application/json"}, "body": "{\n    \"admin_number\": \"5047\",\n    \"last4_card_number\": \"9194\",\n    \"res_code_override\": \"85\",\n    \"reason\": \"Accepted\",\n    \"FalConReasonCode1\": \"21\",\n    \"FalConReasonCode2\": \"6\",\n    \"FalConReasonCode3\": \"20\",\n    \"FalConScore1\": \"950\",\n    \"ResDecisionCode\": \"202\",\n    \"ActivationMethodEmail\": [\n        {\n            \"MaskedEmail\": \"a***<EMAIL>\",\n            \"UniqueIdentifierEmail\": \"0ed0e8c8-5a6b-4abc-b51d-c83c4fbdd947\"\n        },\n        {\n            \"MaskedEmail\": \"f***<EMAIL>\",\n            \"UniqueIdentifierEmail\": \"0ed0e8c8-5a6b-4abc-b51d-c83c4fbdd947\"\n        }\n    ],\n    \"ActivationMethodSMS\": [\n        {\n            \"MaskedSMS\": \"(###) ### 4567\",\n            \"UniqueIdentifierSMS\": \"0ed0e8c8-5a7b-4abc-a51d-c72c4fbdd946\"\n        },\n        {\n            \"MaskedSMS\": \"(###) ### 7890\",\n            \"UniqueIdentifierSMS\": \"0ed0e8c8-5a7b-4abc-a51d-c72c4fbdd946\"\n        }\n    ]\n}"}}