{"mappings": [{"id": "a1b2c3d4-e5f6-4789-a012-3456789abcde", "priority": 1, "request": {"method": "POST", "urlPath": "/api/peerPayments", "headers": {"Content-Type": {"equalTo": "application/json"}}, "bodyPatterns": [{"matchesJsonPath": "$[?(@.amount == 0.91)]"}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"personId": "{{jsonPath request.body '$.personId'}}", "amount": "{{jsonPath request.body '$.amount'}}", "currencyCode": "{{jsonPath request.body '$.currencyCode'}}", "referenceId": "{{jsonPath request.body '$.referenceId'}}", "reasonCode": "{{jsonPath request.body '$.reasonCode'}}", "paymentResponseDetails": {"decision": "Approved"}}}}, {"id": "b2c3d4e5-f6a7-4890-b123-456789abcdef", "priority": 1, "request": {"method": "POST", "urlPath": "/api/peerPayments", "headers": {"Content-Type": {"equalTo": "application/json"}}, "bodyPatterns": [{"matchesJsonPath": "$[?(@.amount == 5.03)]"}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"paymentResponseDetails": {"decision": "Declined", "reasonCode": "99999", "errorCode": {"name": "PEER_PAYMENT_NOT_REACHABLE", "message": ""}}}}}, {"priority": 1, "request": {"method": "POST", "urlPath": "/api/peerPayments", "headers": {"Content-Type": {"equalTo": "application/json"}}, "bodyPatterns": [{"matchesJsonPath": "$[?(@.amount == 0.22)]"}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"personId": "{{jsonPath request.body '$.personId'}}", "amount": "{{jsonPath request.body '$.amount'}}", "currencyCode": "{{jsonPath request.body '$.currencyCode'}}", "referenceId": "{{jsonPath request.body '$.referenceId'}}", "reasonCode": "{{jsonPath request.body '$.reasonCode'}}", "paymentResponseDetails": {"decision": "Declined", "reasonCode": "40200", "errorCode": {"name": "INSUFFICIENT_FUNDS_IN_OMNIBUS"}}}}}, {"priority": 1, "request": {"method": "POST", "urlPath": "/api/peerPayments", "headers": {"Content-Type": {"equalTo": "application/json"}}, "bodyPatterns": [{"matchesJsonPath": "$[?(@.amount == 0.4 || @.amount == 4.0)]"}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"personId": "{{jsonPath request.body '$.personId'}}", "amount": "{{jsonPath request.body '$.amount'}}", "currencyCode": "{{jsonPath request.body '$.currencyCode'}}", "referenceId": "{{jsonPath request.body '$.referenceId'}}", "reasonCode": "{{jsonPath request.body '$.reasonCode'}}", "paymentResponseDetails": {"decision": "Declined", "reasonCode": "9999", "errorCode": {"name": "BAD_REQUEST", "message": ""}}}}}, {"priority": 1, "request": {"method": "POST", "urlPath": "/api/peerPayments", "headers": {"Content-Type": {"equalTo": "application/json"}}, "bodyPatterns": [{"matchesJsonPath": "$[?(@.amount == 0.99)]"}]}, "response": {"status": 200, "fixedDelayMilliseconds": 10000, "headers": {"Content-Type": "application/json"}, "jsonBody": {"paymentResponseDetails": {"decision": "Declined", "reasonCode": "99999", "errorCode": {"name": "TimeOut"}}}}}, {"priority": 1, "request": {"method": "POST", "urlPath": "/api/peerPayments", "headers": {"Content-Type": {"equalTo": "application/json"}}, "bodyPatterns": [{"matchesJsonPath": "$[?(@.amount == 0.62)]"}]}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"personId": "{{jsonPath request.body '$.personId'}}", "amount": "{{jsonPath request.body '$.amount'}}", "currencyCode": "{{jsonPath request.body '$.currencyCode'}}", "referenceId": "{{jsonPath request.body '$.referenceId'}}", "reasonCode": "{{jsonPath request.body '$.reasonCode'}}", "paymentResponseDetails": {"decision": "Declined", "reasonCode": "40602", "errorCode": {"name": "ROLLING_SEND_LIMIT_REACHED"}}}}}, {"priority": 10, "request": {"method": "POST", "urlPath": "/api/peerPayments", "headers": {"Content-Type": {"equalTo": "application/json"}}}, "response": {"status": 200, "headers": {"Content-Type": "application/json"}, "jsonBody": {"personId": "{{jsonPath request.body '$.personId'}}", "amount": "{{jsonPath request.body '$.amount'}}", "currencyCode": "{{jsonPath request.body '$.currencyCode'}}", "referenceId": "{{jsonPath request.body '$.referenceId'}}", "reasonCode": "{{jsonPath request.body '$.reasonCode'}}", "paymentResponseDetails": {"decision": "Approved"}}}}]}