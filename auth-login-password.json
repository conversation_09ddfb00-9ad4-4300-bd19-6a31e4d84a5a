{
    "request": {
        "method": "POST",
        "urlPath": "/auth/login",
        "headers": {
            "Content-Type": {
                "equalTo": "application/json"
            }
        },
        "bodyPatterns": [
            {
                "matchesJsonPath": "$.username"
            },
            {
                "matchesJsonPath": "$.password"
            }
        ]
    },
    "response": {
        "status": 200,
        "transformers": ["PasswordAuthTransformer"],
        "headers": {
            "Content-Type": "application/json"
        },
        "body": "{
            \"message\": \"Login successful\",
            \"user\": \"{{request.body 'username'}}\",
            \"authenticated\": true
        }"
    }
}