import com.github.tomakehurst.wiremock.extension.ResponseTransformerV2;
import com.github.tomakehurst.wiremock.http.Request;
import com.github.tomakehurst.wiremock.http.Response;
import com.github.tomakehurst.wiremock.stubbing.ServeEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import java.util.List;

public class JwtRefreshTransformer implements ResponseTransformerV2  {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public String getName() {
        return "jwt-refresh";
    }
    
    @Override
    public Response transform(Response response, ServeEvent serveEvent) {

        // CRITICAL FIX: Only process if this transformer is explicitly configured for this mapping
        List<String> configuredTransformers = serveEvent.getStubMapping().getResponse().getTransformers();
        if (configuredTransformers == null || !configuredTransformers.contains("jwt-refresh")) {
            System.out.println("=== JWT Refresh SKIPPED (not configured for this mapping) ===");
            return response; // Skip processing if not configured for this mapping
        }

        Request request = serveEvent.getRequest();
        String authHeader = request.getHeader("Authorization");
        
        System.out.println("Processing token refresh request");
        
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return createUnauthorizedResponse("Missing or invalid authorization header");
        }
        
        String userId = JwtResponseTransformer.extractUserFromToken(authHeader);
        if (userId == null) {
            return createUnauthorizedResponse("Invalid token format");
        }
        
        try {
            System.out.println("Generating refreshed token for user: " + userId);
            String newToken = JwtResponseTransformer.issueNewToken(userId);
            ObjectNode jsonResponse = objectMapper.createObjectNode();
            jsonResponse.put("access_token", newToken);
            jsonResponse.put("token_type", "Bearer");
            jsonResponse.put("expires_in", JwtResponseTransformer.getTokenExpirySeconds());
            jsonResponse.put("issued_at", System.currentTimeMillis() / 1000);
            jsonResponse.put("user_id", userId);

            return Response.Builder.like(response)
                    .but()
                    .headers(response.getHeaders().plus(new com.github.tomakehurst.wiremock.http.HttpHeader("Content-Type", "application/json")))
                    .body(objectMapper.writeValueAsString(jsonResponse))
                    .build();
        } catch (Exception e) {
            System.err.println("Error generating refreshed token: " + e.getMessage());
            return Response.Builder.like(response)
                    .but()
                    .status(500)
                    .body("{\"error\":\"Token refresh failed\"}")
                    .build();
        }
    }
    
    private Response createUnauthorizedResponse(String message) {
        try {
            ObjectNode errorResponse = objectMapper.createObjectNode();
            errorResponse.put("error", "Unauthorized");
            errorResponse.put("message", message);
            
            return Response.response()
                    .status(401)
                    .headers(new com.github.tomakehurst.wiremock.http.HttpHeaders(new com.github.tomakehurst.wiremock.http.HttpHeader("Content-Type", "application/json")))
                    .body(objectMapper.writeValueAsString(errorResponse))
                    .build();
        } catch (Exception e) {
            return Response.response()
                    .status(401)
                    .body("{\"error\":\"Unauthorized\"}")
                    .build();
        }
    }
    
}
