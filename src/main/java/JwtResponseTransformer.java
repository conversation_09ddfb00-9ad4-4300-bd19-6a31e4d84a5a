import com.github.tomakehurst.wiremock.extension.ResponseTransformerV2;
import com.github.tomakehurst.wiremock.http.Request;
import com.github.tomakehurst.wiremock.http.Response;
 
import com.github.tomakehurst.wiremock.stubbing.ServeEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import java.util.List;

public class JwtResponseTransformer implements ResponseTransformerV2 {
    
    private static final Map<String, TokenInfo> tokenStore = new ConcurrentHashMap<>();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final int TOKEN_EXPIRY_MINUTES = 2; // Short expiry for testing
    
    @Override
    public String getName() {
        return "jwt-generator";
    }
    
    @Override
    public Response transform(Response response, ServeEvent serveEvent) {
        // CRITICAL FIX: Only process if this transformer is explicitly configured for this mapping
        List<String> configuredTransformers = serveEvent.getStubMapping().getResponse().getTransformers();
        if (configuredTransformers == null || !configuredTransformers.contains("jwt-generator")) {
            System.out.println("=== JWT GENERATOR SKIPPED (not configured for this mapping) ===");
            return response; // Skip processing if not configured for this mapping
        }

        try {
            Request request = serveEvent.getRequest();
            System.out.println("=== JWT GENERATOR TRANSFORMER CALLED ===");
            System.out.println("Request URL: " + request.getUrl());
            System.out.println("Request Method: " + request.getMethod());
            System.out.println("Request Body: " + request.getBodyAsString());
            System.out.println("Mapping Name: " + serveEvent.getStubMapping().getName());

            String userId = extractUserId(request);
            String newToken = generateOrRefreshToken(userId);
            
            ObjectNode jsonResponse = objectMapper.createObjectNode();
            jsonResponse.put("access_token", newToken);
            jsonResponse.put("token_type", "Bearer");
            jsonResponse.put("expires_in", TOKEN_EXPIRY_MINUTES * 60);
            jsonResponse.put("issued_at", Instant.now().getEpochSecond());
            jsonResponse.put("user_id", userId);
            System.out.println("Generated token: " + newToken);
            System.out.println("jsonResponse: " + objectMapper.writeValueAsString(jsonResponse));
            
            return Response.Builder.like(response)
                    .but()
                    .headers(response.getHeaders().plus(new com.github.tomakehurst.wiremock.http.HttpHeader("Content-Type", "application/json")))
                    .body(objectMapper.writeValueAsString(jsonResponse))
                    .build();
                    
        } catch (Exception e) {
            System.err.println("Error extracting user ID: " + e.getMessage());
        }
        return response;
    }

    private String extractUserId(Request request) throws Exception {
        String body = request.getBodyAsString();
        if (body == null || body.isEmpty()) {
            throw new IllegalArgumentException("Empty request body; cannot extract user id");
        }
        ObjectNode json = (ObjectNode) objectMapper.readTree(body);
        if (json.hasNonNull("username")) {
            return json.get("username").asText();
        }
        if (json.hasNonNull("user") ) {
            return json.get("user").asText();
        }
        if (json.hasNonNull("user_id")) {
            return json.get("user_id").asText();
        }
        throw new IllegalArgumentException("username/user/user_id not found in body");
    }
    
    private String generateOrRefreshToken(String userId) {
        TokenInfo existingToken = tokenStore.get(userId);
        
        if (existingToken != null && !isTokenExpired(existingToken)) {
            System.out.println("Returning existing valid token for user: " + userId);
            return existingToken.token;
        }
        
        System.out.println("Generating new token for user: " + userId);
        String newToken = generateJWT(userId);
        TokenInfo tokenInfo = new TokenInfo(newToken, Instant.now().plus(TOKEN_EXPIRY_MINUTES, ChronoUnit.MINUTES));
        System.out.println("Token expires at: " + tokenInfo.expiresAt);
        tokenStore.put(userId, tokenInfo);
        
        return newToken;
    }
    
    private boolean isTokenExpired(TokenInfo tokenInfo) {
        boolean expired = Instant.now().isAfter(tokenInfo.expiresAt);
        if (expired) {
            System.out.println("Token expired at: " + tokenInfo.expiresAt);
        }
        return expired;
    }
    
    private static String generateJWT(String userId) {
        try {
            ObjectNode header = objectMapper.createObjectNode();
            header.put("alg", "HS256");
            header.put("typ", "JWT");
            String encodedHeader = Base64.getUrlEncoder().withoutPadding()
                    .encodeToString(objectMapper.writeValueAsBytes(header));
            
            long now = Instant.now().getEpochSecond();
            long exp = Instant.now().plus(TOKEN_EXPIRY_MINUTES, ChronoUnit.MINUTES).getEpochSecond();
            
            ObjectNode payload = objectMapper.createObjectNode();
            payload.put("sub", userId);
            payload.put("iss", "wiremock-auth");
            payload.put("aud", "test-api");
            payload.put("iat", now);
            payload.put("exp", exp);
            payload.put("jti", UUID.randomUUID().toString());
            
            String encodedPayload = Base64.getUrlEncoder().withoutPadding()
                    .encodeToString(objectMapper.writeValueAsBytes(payload));
            
            String signature = Base64.getUrlEncoder().withoutPadding()
                    .encodeToString(("signature_" + userId + "_" + now).getBytes());
            
            System.out.println("Generated JWT: " + encodedHeader + "." + encodedPayload + "." + signature);
            return encodedHeader + "." + encodedPayload + "." + signature;
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate JWT", e);
        }
    }

    public static String issueNewToken(String userId) {
        String newToken = generateJWT(userId);
        TokenInfo tokenInfo = new TokenInfo(newToken, Instant.now().plus(TOKEN_EXPIRY_MINUTES, ChronoUnit.MINUTES));
        tokenStore.put(userId, tokenInfo);
        return newToken;
    }

    public static int getTokenExpirySeconds() {
        return TOKEN_EXPIRY_MINUTES * 60;
    }
    
    public static boolean isValidToken(String token, String userId) {
        if (token == null || !token.startsWith("Bearer ")) {
            return false;
        }
        
        String jwtToken = token.substring(7);
        TokenInfo storedToken = tokenStore.get(userId);
        
        boolean valid = storedToken != null && 
                       storedToken.token.equals(jwtToken) && 
                       !Instant.now().isAfter(storedToken.expiresAt);
        
        System.out.println("Token validation for user " + userId + ": " + valid);
        return valid;
    }
    
    public static String extractUserFromToken(String token) {
        try {
            if (token == null || !token.startsWith("Bearer ")) {
                return null;
            }
            
            String jwtToken = token.substring(7);
            String[] parts = jwtToken.split("\\.");
            if (parts.length != 3) {
                return null;
            }
            
            String payload = new String(Base64.getUrlDecoder().decode(parts[1]));
            ObjectNode payloadJson = (ObjectNode) objectMapper.readTree(payload);
            String userId = payloadJson.get("sub").asText();
            System.out.println("Extracted user from token: " + userId);
            return userId;
            
        } catch (Exception e) {
            System.err.println("Error extracting user from token: " + e.getMessage());
            return null;
        }
    }
    
    private static class TokenInfo {
        final String token;
        final Instant expiresAt;
        
        TokenInfo(String token, Instant expiresAt) {
            this.token = token;
            this.expiresAt = expiresAt;
        }
    }
}
