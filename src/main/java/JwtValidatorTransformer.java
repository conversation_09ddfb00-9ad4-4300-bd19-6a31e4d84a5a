import com.github.tomakehurst.wiremock.extension.ResponseTransformerV2;
import com.github.tomakehurst.wiremock.http.Request;
import com.github.tomakehurst.wiremock.http.Response;
 
import com.github.tomakehurst.wiremock.stubbing.ServeEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;
import com.github.tomakehurst.wiremock.http.HttpHeaders;
import com.github.tomakehurst.wiremock.http.HttpHeader;
import java.util.List;

public class JwtValidatorTransformer implements ResponseTransformerV2 {
    
    private static final ObjectMapper objectMapper = new ObjectMapper();
    
    @Override
    public String getName() {
        return "jwt-validator";
    }
    
    @Override
    public Response transform(Response response, ServeEvent serveEvent) {
        Request request = serveEvent.getRequest();

        // CRITICAL FIX: Only process if this transformer is explicitly configured for this mapping
        List<String> configuredTransformers = serveEvent.getStubMapping().getResponse().getTransformers();
        if (configuredTransformers == null || !configuredTransformers.contains("jwt-validator")) {
            System.out.println("=== JWT VALIDATOR SKIPPED (not configured for this mapping) ===");
            return response; // Skip processing if not configured for this mapping
        }

        HttpHeaders headers = request.getHeaders();

        for (HttpHeader header : headers.all()) {
            String name = header.key();
            List<String> values = header.values();
            System.out.println("Header: " + name + " = " + values);
        }

        String authHeader = request.getHeader("Authorization");

        System.out.println("=== JWT VALIDATOR TRANSFORMER CALLED ===");
        System.out.println("Request URL: " + request.getUrl());
        System.out.println("Request Method: " + request.getMethod());
        System.out.println("Mapping Name: " + serveEvent.getStubMapping().getName());
        System.out.println("Validating request to: " + request.getUrl());
        System.out.println("authHeader: " + authHeader);
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            return createUnauthorizedResponse("Missing or invalid authorization header");
        }
        
        String userId = JwtResponseTransformer.extractUserFromToken(authHeader);
        if (userId == null) {
            return createUnauthorizedResponse("Invalid token format");
        }
        
        if (!JwtResponseTransformer.isValidToken(authHeader, userId)) {
            return createUnauthorizedResponse("Token expired or invalid");
        }
        
        System.out.println("Token validation successful for user: " + userId);
        
        try {
            String responseBody = response.getBodyAsString();
            if (responseBody != null && responseBody.contains("extracted_from_jwt")) {
                responseBody = responseBody.replace("extracted_from_jwt", userId);
                return Response.Builder.like(response)
                        .but()
                        .body(responseBody)
                        .build();
            }
        } catch (Exception e) {
            System.err.println("Error modifying response body: " + e.getMessage());
        }
        
        return response;
    }
    
    private Response createUnauthorizedResponse(String message) {
        try {
            System.out.println("Creating unauthorized response: " + message);
            ObjectNode errorResponse = objectMapper.createObjectNode();
            errorResponse.put("error", "Unauthorized");
            errorResponse.put("message", message);
            errorResponse.put("timestamp", System.currentTimeMillis());
            
            return Response.response()
                    .status(401)
                    .headers(new com.github.tomakehurst.wiremock.http.HttpHeaders(new com.github.tomakehurst.wiremock.http.HttpHeader("Content-Type", "application/json")))
                    .body(objectMapper.writeValueAsString(errorResponse))
                    .build();
        } catch (Exception e) {
            return Response.response()
                    .status(401)
                    .body("{\"error\":\"Unauthorized\"}")
                    .build();
        }
    }
}
