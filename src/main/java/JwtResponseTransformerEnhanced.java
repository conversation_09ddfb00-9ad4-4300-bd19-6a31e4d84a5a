import com.github.tomakehurst.wiremock.extension.ResponseTransformerV2;
import com.github.tomakehurst.wiremock.http.Request;
import com.github.tomakehurst.wiremock.http.Response;
 
import com.github.tomakehurst.wiremock.stubbing.ServeEvent;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ObjectNode;

import java.util.Base64;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.UUID;
import java.util.List;

public class JwtResponseTransformerEnhanced implements ResponseTransformerV2 {
    
    private static final Map<String, TokenInfo> tokenStore = new ConcurrentHashMap<>();
    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final int TOKEN_EXPIRY_MINUTES = 15; // Standard 15 minutes
    private static final int REFRESH_TOKEN_EXPIRY_DAYS = 30; // 30 days
    
    @Override
    public String getName() {
        return "jwt-generator-enhanced";
    }
    
    @Override
    public Response transform(Response response, ServeEvent serveEvent) {
        List<String> configuredTransformers = serveEvent.getStubMapping().getResponse().getTransformers();
        if (configuredTransformers == null || !configuredTransformers.contains("jwt-generator-enhanced")) {
            return response;
        }

        try {
            Request request = serveEvent.getRequest();
            System.out.println("=== ENHANCED JWT GENERATOR CALLED ===");
            System.out.println("Request Body: " + request.getBodyAsString());

            ObjectNode requestJson = (ObjectNode) objectMapper.readTree(request.getBodyAsString());
            String grantType = requestJson.has("grant_type") ? requestJson.get("grant_type").asText() : "password";
            
            System.out.println("Grant Type: " + grantType);
            
            ObjectNode jsonResponse = objectMapper.createObjectNode();
            
            switch (grantType.toLowerCase()) {
                case "password":
                    handlePasswordGrant(requestJson, jsonResponse);
                    break;
                case "refresh_token":
                    handleRefreshTokenGrant(requestJson, jsonResponse);
                    break;
                case "client_credentials":
                    handleClientCredentialsGrant(requestJson, jsonResponse);
                    break;
                default:
                    return createErrorResponse("unsupported_grant_type", "Grant type '" + grantType + "' is not supported");
            }
            
            return Response.Builder.like(response)
                    .but()
                    .headers(response.getHeaders().plus(new com.github.tomakehurst.wiremock.http.HttpHeader("Content-Type", "application/json")))
                    .body(objectMapper.writeValueAsString(jsonResponse))
                    .build();
                    
        } catch (Exception e) {
            System.err.println("Error in JWT transformer: " + e.getMessage());
            return createErrorResponse("invalid_request", "Invalid request format");
        }
    }
    
    private void handlePasswordGrant(ObjectNode request, ObjectNode response) throws IllegalArgumentException {
        String username = getRequiredField(request, "username");
        String password = getRequiredField(request, "password");
        
        // Basic validation (you can enhance this)
        if (!isValidCredentials(username, password)) {
            throw new IllegalArgumentException("Invalid credentials");
        }
        
        String accessToken = generateAccessToken(username);
        String refreshToken = generateRefreshToken(username);
        
        response.put("access_token", accessToken);
        response.put("token_type", "Bearer");
        response.put("expires_in", TOKEN_EXPIRY_MINUTES * 60);
        response.put("refresh_token", refreshToken);
        response.put("scope", "read write");
        
        System.out.println("Password grant successful for user: " + username);
    }
    
    private void handleRefreshTokenGrant(ObjectNode request, ObjectNode response) throws IllegalArgumentException {
        String refreshToken = getRequiredField(request, "refresh_token");
        
        String username = validateRefreshToken(refreshToken);
        if (username == null) {
            throw new IllegalArgumentException("Invalid refresh token");
        }
        
        String newAccessToken = generateAccessToken(username);
        String newRefreshToken = generateRefreshToken(username);
        
        response.put("access_token", newAccessToken);
        response.put("token_type", "Bearer");
        response.put("expires_in", TOKEN_EXPIRY_MINUTES * 60);
        response.put("refresh_token", newRefreshToken);
        response.put("scope", "read write");
        
        System.out.println("Refresh token grant successful for user: " + username);
    }
    
    private void handleClientCredentialsGrant(ObjectNode request, ObjectNode response) throws IllegalArgumentException {
        String clientId = getRequiredField(request, "client_id");
        String clientSecret = request.has("client_secret") ? request.get("client_secret").asText() : null;
        
        // Validate client credentials
        if (!isValidClient(clientId, clientSecret)) {
            throw new IllegalArgumentException("Invalid client credentials");
        }
        
        String accessToken = generateAccessToken(clientId);
        
        response.put("access_token", accessToken);
        response.put("token_type", "Bearer");
        response.put("expires_in", TOKEN_EXPIRY_MINUTES * 60);
        response.put("scope", "read");
        
        System.out.println("Client credentials grant successful for client: " + clientId);
    }
    
    private String getRequiredField(ObjectNode json, String fieldName) throws IllegalArgumentException {
        if (!json.has(fieldName) || json.get(fieldName).isNull()) {
            throw new IllegalArgumentException("Missing required field: " + fieldName);
        }
        return json.get(fieldName).asText();
    }
    
    private boolean isValidCredentials(String username, String password) {
        // Simple validation - enhance as needed
        return username != null && password != null && !username.isEmpty() && !password.isEmpty();
    }
    
    private boolean isValidClient(String clientId, String clientSecret) {
        // Simple validation - enhance as needed
        return clientId != null && !clientId.isEmpty();
    }
    
    private String generateAccessToken(String subject) {
        try {
            ObjectNode header = objectMapper.createObjectNode();
            header.put("alg", "HS256");
            header.put("typ", "JWT");
            String encodedHeader = Base64.getUrlEncoder().withoutPadding()
                    .encodeToString(objectMapper.writeValueAsBytes(header));
            
            long now = Instant.now().getEpochSecond();
            long exp = Instant.now().plus(TOKEN_EXPIRY_MINUTES, ChronoUnit.MINUTES).getEpochSecond();
            
            ObjectNode payload = objectMapper.createObjectNode();
            payload.put("sub", subject);
            payload.put("iss", "wiremock-auth-server");
            payload.put("aud", "wiremock-api");
            payload.put("iat", now);
            payload.put("exp", exp);
            payload.put("jti", UUID.randomUUID().toString());
            payload.put("token_type", "access_token");
            
            String encodedPayload = Base64.getUrlEncoder().withoutPadding()
                    .encodeToString(objectMapper.writeValueAsBytes(payload));
            
            String signature = Base64.getUrlEncoder().withoutPadding()
                    .encodeToString(("signature_" + subject + "_" + now).getBytes());
            
            String token = encodedHeader + "." + encodedPayload + "." + signature;
            
            // Store token info
            TokenInfo tokenInfo = new TokenInfo(token, Instant.now().plus(TOKEN_EXPIRY_MINUTES, ChronoUnit.MINUTES), "access");
            tokenStore.put(subject, tokenInfo);
            
            return token;
            
        } catch (Exception e) {
            throw new RuntimeException("Failed to generate access token", e);
        }
    }
    
    private String generateRefreshToken(String subject) {
        String refreshToken = "refresh_" + UUID.randomUUID();
        TokenInfo tokenInfo = new TokenInfo(refreshToken, Instant.now().plus(REFRESH_TOKEN_EXPIRY_DAYS, ChronoUnit.DAYS), "refresh");
        tokenStore.put(subject + "_refresh", tokenInfo);
        return refreshToken;
    }
    
    private String validateRefreshToken(String refreshToken) {
        for (Map.Entry<String, TokenInfo> entry : tokenStore.entrySet()) {
            if (entry.getKey().endsWith("_refresh") && 
                entry.getValue().token.equals(refreshToken) && 
                !Instant.now().isAfter(entry.getValue().expiresAt)) {
                return entry.getKey().replace("_refresh", "");
            }
        }
        return null;
    }
    
    private Response createErrorResponse(String error, String description) {
        try {
            ObjectNode errorResponse = objectMapper.createObjectNode();
            errorResponse.put("error", error);
            errorResponse.put("error_description", description);
            
            return Response.response()
                    .status(400)
                    .headers(new com.github.tomakehurst.wiremock.http.HttpHeaders(
                        new com.github.tomakehurst.wiremock.http.HttpHeader("Content-Type", "application/json")
                    ))
                    .body(objectMapper.writeValueAsString(errorResponse))
                    .build();
        } catch (Exception e) {
            return Response.response().status(500).build();
        }
    }
    
    private static class TokenInfo {
        final String token;
        final Instant expiresAt;
        final String type;
        
        TokenInfo(String token, Instant expiresAt, String type) {
            this.token = token;
            this.expiresAt = expiresAt;
            this.type = type;
        }
    }
}
