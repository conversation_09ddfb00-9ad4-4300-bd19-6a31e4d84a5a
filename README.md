# WireMock Extensions Project

This project contains custom WireMock extensions including JWT authentication transformers and password validation.

## Prerequisites

- Java 21
- Gradle (or use the wrapper)
- WireMock Standalone JAR (currently using `wiremock-standalone-3.13.1.jar`)

## Project Structure

```
├── build.gradle                    # Build configuration
├── extensions/                     # Groovy extensions
│   └── PasswordAuthTransformer.groovy
├── src/main/java/                  # Java transformers
│   ├── JwtResponseTransformer.java
│   ├── JwtValidatorTransformer.java
│   └── JwtRefreshTransformer.java
├── mappings/                       # WireMock stub mappings
└── __files/                        # Static response files
```

## Building the Project

1. **Compile and build the extensions JAR:**
   ```bash
   # ./gradlew clean build
   ./gradle clean build
   ```

2. **The output JAR will be created at:**
   ```
   build/libs/wiremock-extensions.jar
   ```

## Running WireMock with Extensions

1. **Download WireMock standalone (if not already present):**
   ```bash
   wget https://repo1.maven.org/maven2/org/wiremock/wiremock-standalone/3.13.1/wiremock-standalone-3.13.1.jar
   ```

2. **Run WireMock with your extensions:**
   ```bash
   java -cp "wiremock-standalone-3.13.1.jar:build/libs/wiremock-extensions.jar" \
        wiremock.Run \
        --port 8080 \
        --extensions JwtResponseTransformer,JwtResponseTransformerEnhanced,JwtValidatorTransformer,JwtRefreshTransformer,PasswordAuthTransformer
   ```

3. **Alternative with verbose logging:**
   ```bash
   java -cp "wiremock-standalone-3.13.1.jar;build/libs/wiremock-extensions.jar" wiremock.Run --port 8080 --extensions JwtResponseTransformer,JwtResponseTransformerEnhanced,JwtValidatorTransformer,JwtRefreshTransformer,PasswordAuthTransformer --root-dir . --global-response-templating --verbose
   ```

## Available Transformers

- **`jwt-generator`** - Generates JWT tokens for authentication endpoints
- **`jwt-validator`** - Validates JWT tokens in protected endpoints  
- **`jwt-refresh`** - Handles JWT token refresh requests
- **`password-auth-transformer`** - Validates password-based authentication

## Testing the Setup

1. **Check WireMock is running:**
   ```bash
   curl http://localhost:8080/__admin/mappings
   ```

2. **Test a mapping with transformers:**
   ```bash
   curl -X POST http://localhost:8080/test \
        -H "Content-Type: application/json" \
        -d '{"username":"testuser","password":"123","admin_number":"5047","last4_card_number":"9194"}'
   ```

## Development Workflow

1. **Make changes to Java/Groovy files**
2. **Rebuild the extensions:**
   ```bash
   ./gradlew clean build
   ```
3. **Restart WireMock with the updated JAR**

## Troubleshooting

- Ensure Java 21 is installed and `JAVA_HOME` is set correctly
- Check that all required JARs are in the classpath
- Use `--verbose` flag to see detailed WireMock logs
- Verify transformer names match exactly in your mapping files