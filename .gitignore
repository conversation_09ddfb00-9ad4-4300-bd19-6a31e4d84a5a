# WireMock specific
/mappings/**/*.tmp
/mappings/**/*.bak
/stubs/**/*.tmp
/stubs/**/*.bak
/__files/**/*.tmp
/__files/**/*.bak

# Ignore generated recordings (if you don't want to commit them)
/recordings/
/wiremock-standalone*.jar

# Logs
logs/
*.log

# IDE specific
.idea/
.vscode/
*.iml

# Java/Maven/Gradle
target/
build/
out/
.gradle/
.mvn/
!gradle/wrapper/gradle-wrapper.jar

# OS specific
.DS_Store
Thumbs.db

# Environment files
.env
*.local