import com.github.tomakehurst.wiremock.extension.ResponseTransformer
import com.github.tomakehurst.wiremock.http.Request
import com.github.tomakehurst.wiremock.http.Response
import com.github.tomakehurst.wiremock.http.ResponseDefinition
import com.github.tomakehurst.wiremock.http.HttpHeaders
import com.github.tomakehurst.wiremock.http.HttpHeader
import com.github.tomakehurst.wiremock.common.FileSource
import com.github.tomakehurst.wiremock.extension.Parameters

class PasswordAuthTransformer extends ResponseTransformer {
    
    PasswordAuthTransformer() {
        println "=== PasswordAuthTransformer constructor called ==="
    }
    
    @Override
    String getName() {
        println "=== getName() called, returning: password-auth-transformer ==="
        return "password-auth-transformer"
    }
    
    @Override
    boolean applyGlobally() {
        return false
    }
    
    @Override
    Response transform(Request request, Response response, FileSource files, Parameters parameters) {
        try {
            println "=== PasswordAuthTransformer invoked ==="
            println "Request body: ${request.getBodyAsString()}"
            
            def jsonBody = new groovy.json.JsonSlurper().parseText(request.getBodyAsString())
            println "Parsed JSON: ${jsonBody}"
            
            def password = jsonBody?.password
            def userId = jsonBody?.user_id
            def adminNumber = jsonBody?.admin_number
            def last4CardNumber = jsonBody?.last4_card_number
            
            println "Extracted values: userId=${userId}, password=${password}, adminNumber=${adminNumber}, last4CardNumber=${last4CardNumber}"
            
            // Check if required fields are present
            if (!userId || !password || !adminNumber || !last4CardNumber) {
                println "Missing required fields - returning 400"
                return Response.response()
                    .status(400)
                    .body('{"error": "Missing required fields"}')
                    .headers(new com.github.tomakehurst.wiremock.http.HttpHeaders(
                        new com.github.tomakehurst.wiremock.http.HttpHeader('Content-Type', 'application/json')
                    ))
                    .build()
            }
            
            // Validate the specific values
            if (userId != 'TestUser' || password != '123' || adminNumber != '5047' || last4CardNumber != '9194') {
                println "Invalid credentials - returning 400"
                return Response.response()
                    .status(400)
                    .body('{"error": "Invalid credentials"}')
                    .headers(new com.github.tomakehurst.wiremock.http.HttpHeaders(
                        new com.github.tomakehurst.wiremock.http.HttpHeader('Content-Type', 'application/json')
                    ))
                    .build()
            }
            
            println "All validations passed - returning original response"
            return response
        } catch (Exception e) {
            println "Exception occurred: ${e.message}"
            return Response.response()
                .status(400)
                .body('{"error": "Invalid JSON"}')
                .headers(new com.github.tomakehurst.wiremock.http.HttpHeaders(
                    new com.github.tomakehurst.wiremock.http.HttpHeader('Content-Type', 'application/json')
                ))
                .build()
        }
    }
}